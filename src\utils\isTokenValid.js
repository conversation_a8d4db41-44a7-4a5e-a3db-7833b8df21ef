import { decodeJWTPayload, isTokenNotExpired } from './base64Utils.js';

export function isTokenValid(token) {
    if (!token) {
      return false;
    }

    try {
      // Use the robust decoder that handles special characters
      const payload = decodeJWTPayload(token);

      if (!payload) {
        return false;
      }

      // Check if token is not expired
      return isTokenNotExpired(payload);
    } catch (error) {
      return false;
    }
  }
  