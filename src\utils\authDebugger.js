/**
 * Authentication Debugging Utility
 * Helps identify and debug authentication issues, especially with special characters
 */

export const debugAuthFlow = (step, data = {}) => {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.group(`🔐 Auth Debug: ${step}`);
  console.log('Timestamp:', new Date().toISOString());
  
  switch (step) {
    case 'LOGIN_REQUEST':
      console.log('Email:', data.email);
      console.log('Password length:', data.password?.length);
      break;
      
    case 'LOGIN_RESPONSE':
      console.log('Status:', data.status);
      console.log('User data:', data.user);
      console.log('Token present:', !!data.access_token);
      console.log('Token length:', data.access_token?.length);
      
      if (data.user?.username) {
        console.group('Username Analysis');
        console.log('Username:', data.user.username);
        console.log('Has special chars:', /[^\x00-\x7F]/.test(data.user.username));
        console.log('Has CJK chars:', /[\u4e00-\u9fff\u3400-\u4dbf\u3040-\u309f\u30a0-\u30ff]/.test(data.user.username));
        console.log('Character codes:', data.user.username.split('').map(c => c.charCodeAt(0)));
        console.groupEnd();
      }
      break;
      
    case 'TOKEN_STORAGE':
      console.log('Storing token:', !!data.access_token);
      console.log('Storing user:', !!data.user);
      
      // Test storage immediately
      try {
        const testKey = 'auth_debug_test_' + Date.now();
        localStorage.setItem(testKey, JSON.stringify(data.user));
        const retrieved = JSON.parse(localStorage.getItem(testKey));
        localStorage.removeItem(testKey);
        
        console.log('✅ Storage test passed');
        console.log('Username preserved:', data.user?.username === retrieved?.username);
      } catch (error) {
        console.error('❌ Storage test failed:', error);
      }
      break;
      
    case 'TOKEN_RETRIEVAL':
      const storedToken = localStorage.getItem('access_token');
      const storedUser = localStorage.getItem('user');
      
      console.log('Token retrieved:', !!storedToken);
      console.log('User retrieved:', !!storedUser);
      
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          console.log('User parsed successfully:', !!parsedUser);
          console.log('Username:', parsedUser?.username);
        } catch (error) {
          console.error('❌ Failed to parse stored user:', error);
        }
      }
      break;
      
    case 'API_REQUEST':
      const token = localStorage.getItem('access_token');
      console.log('Token available for API:', !!token);
      console.log('Token starts with Bearer:', token?.startsWith('Bearer'));
      console.log('Request URL:', data.url);
      console.log('Request method:', data.method);
      break;
      
    default:
      console.log('Data:', data);
  }
  
  console.groupEnd();
};

export const validateAuthState = () => {
  console.group('🔍 Auth State Validation');

  const token = localStorage.getItem('access_token');
  const userString = localStorage.getItem('user');

  console.log('Token present:', !!token);
  console.log('User string present:', !!userString);

  if (token) {
    console.log('Token length:', token.length);
    console.log('Token format valid:', token.includes('.'));

    // Try to decode JWT token
    try {
      const parts = token.split('.');
      if (parts.length === 3) {
        const header = JSON.parse(atob(parts[0]));
        const payload = JSON.parse(atob(parts[1]));

        console.log('✅ JWT token structure valid');
        console.log('Header:', header);
        console.log('Payload:', payload);
        console.log('Token expires:', new Date(payload.exp * 1000));
        console.log('Token valid:', payload.exp > Date.now() / 1000);

        // Check if username in token matches stored user
        if (userString) {
          try {
            const user = JSON.parse(userString);
            console.log('Username in token:', payload.username);
            console.log('Username in storage:', user.username);
            console.log('Usernames match:', payload.username === user.username);
          } catch (e) {
            console.warn('Could not compare usernames:', e);
          }
        }
      } else {
        console.error('❌ Invalid JWT structure - should have 3 parts');
      }
    } catch (error) {
      console.error('❌ Failed to decode JWT:', error);
      console.log('Token preview:', token.substring(0, 50) + '...');
    }
  }

  if (userString) {
    try {
      const user = JSON.parse(userString);
      console.log('✅ User JSON valid');
      console.log('User ID:', user?.id);
      console.log('Username:', user?.username);
      console.log('Email:', user?.email);

      if (user?.username) {
        console.log('Username has special chars:', /[^\x00-\x7F]/.test(user.username));
      }
    } catch (error) {
      console.error('❌ User JSON invalid:', error);
      console.log('Raw user string:', userString);
    }
  }

  console.groupEnd();
};

export const testSpecialCharacterAuth = () => {
  console.group('🧪 Special Character Auth Test');
  
  const testUsers = [
    { username: '高雄富野渡假酒店', description: 'Chinese characters' },
    { username: 'café', description: 'Accented characters' },
    { username: 'Москва', description: 'Cyrillic' },
    { username: 'العربية', description: 'Arabic' },
    { username: '🎮🎯', description: 'Emojis' },
    { username: 'normal_user', description: 'ASCII only' }
  ];
  
  testUsers.forEach((testCase, index) => {
    console.group(`Test ${index + 1}: ${testCase.description}`);
    
    const mockUser = {
      id: index + 1,
      username: testCase.username,
      email: `test${index}@example.com`
    };
    
    const mockToken = `test_token_${index}_${Date.now()}`;
    
    try {
      // Test storage
      const userKey = `test_user_${index}`;
      const tokenKey = `test_token_${index}`;
      
      localStorage.setItem(tokenKey, mockToken);
      localStorage.setItem(userKey, JSON.stringify(mockUser));
      
      // Test retrieval
      const retrievedToken = localStorage.getItem(tokenKey);
      const retrievedUserString = localStorage.getItem(userKey);
      const retrievedUser = JSON.parse(retrievedUserString);
      
      console.log('✅ Storage/retrieval successful');
      console.log('Token match:', mockToken === retrievedToken);
      console.log('Username preserved:', mockUser.username === retrievedUser.username);
      
      // Cleanup
      localStorage.removeItem(userKey);
      localStorage.removeItem(tokenKey);
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
    
    console.groupEnd();
  });
  
  console.groupEnd();
};
